/**
 * Main layout component for the admin panel.
 * This component provides the overall structure including navigation, header, and content areas
 * with responsive design and consistent styling throughout the admin interface.
 */

import React from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  Home,
  Users,
  Settings,
  BarChart3,
  Bot,
  ExternalLink,
  Menu,
  X
} from 'lucide-react';
import { useState, useEffect } from 'react';
import styles from './Layout.module.css';

const Layout: React.FC = () => {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/', icon: Home },
    { name: 'Clients', href: '/clients', icon: Users },
    { name: 'Bot Management', href: '/bot', icon: Bot },
    { name: 'Analytics', href: '/analytics', icon: BarChart3 },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  // Close sidebar when route changes on mobile
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Handle escape key to close sidebar
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && sidebarOpen) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [sidebarOpen]);

  return (
    <div className={styles.container}>
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className={styles.backdrop}
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <div
        className={`${styles.sidebar} ${sidebarOpen ? styles.sidebarOpen : ''}`}
        role="navigation"
        aria-label="Main navigation"
      >
        {/* Sidebar Header */}
        <div className={styles.sidebarHeader}>
          <div className={styles.sidebarHeaderContent}>
            <div className={styles.sidebarLogo}>
              <Bot className="w-5 h-5 text-white" />
            </div>
            <h1 className={styles.sidebarTitle}>AI Answer Bot</h1>
          </div>
          <button
            className={styles.sidebarCloseButton}
            onClick={() => setSidebarOpen(false)}
            aria-label="Close sidebar"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className={`${styles.navigation} sidebar-scroll`}>
          {navigation.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`sidebar-nav-item ${styles.navItem} ${active ? styles.navItemActive : ''}`}
                aria-current={active ? 'page' : undefined}
              >
                <Icon className={`${styles.navIcon} ${active ? styles.navIconActive : ''}`} />
                <span className={styles.navText}>{item.name}</span>
                {active && (
                  <div className={styles.navIndicator}></div>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Sidebar Footer */}
        <div className={styles.sidebarFooter}>
          <div className={styles.footerContent}>
            <ExternalLink className={styles.footerIcon} />
            <span>Version 1.0.0</span>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className={styles.mainContent}>
        {/* Mobile top bar */}
        <div className={styles.mobileTopBar}>
          <button
            className={styles.mobileMenuButton}
            onClick={() => setSidebarOpen(true)}
            aria-label="Open sidebar"
          >
            <Menu className="w-6 h-6" />
          </button>
          <div className={styles.mobileTitle}>
            <h1 className={styles.mobileTitleText}>AI Answer Bot Admin</h1>
          </div>
        </div>

        {/* Page content */}
        <main className={styles.pageContent}>
          <div className={styles.pageContentInner}>
            <div className={styles.pageContainer}>
              <Outlet />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
