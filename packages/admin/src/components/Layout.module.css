/* Layout component styles */

.container {
  min-height: 100vh;
  background-color: var(--color-gray-50);
}

.backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: var(--z-40);
  background-color: rgba(0, 0, 0, 0.5);
}

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: var(--z-50);
  width: var(--spacing-64);
  background-color: white;
  box-shadow: var(--shadow-xl);
  transform: translateX(-100%);
  transition: all 0.3s ease-in-out;
  border-right: 1px solid var(--color-gray-200);
}

.sidebarOpen {
  transform: translateX(0);
}

.sidebarHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--spacing-16);
  padding: 0 var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: linear-gradient(to right, var(--color-primary-50), white);
}

.sidebarHeaderContent {
  display: flex;
  align-items: center;
}

.sidebarLogo {
  width: var(--spacing-8);
  height: var(--spacing-8);
  background-color: var(--color-primary-600);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
}

.sidebarTitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebarCloseButton {
  padding: 6px;
  border-radius: var(--radius-md);
  color: var(--color-gray-400);
  transition: var(--transition-all);
}

.sidebarCloseButton:hover {
  color: var(--color-gray-600);
  background-color: white;
  box-shadow: var(--shadow-sm);
}

.sidebarCloseButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

.navigation {
  flex: 1;
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-6);
  overflow-y: auto;
}

.navItem {
  display: flex;
  align-items: center;
  padding: 10px var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease-in-out;
  position: relative;
  color: var(--color-gray-700);
  text-decoration: none;
  margin-bottom: var(--spacing-1);
}

.navItem:hover {
  background-color: var(--color-gray-50);
  color: var(--color-gray-900);
}

.navItemActive {
  background-color: var(--color-primary-50);
  color: var(--color-primary-700);
  box-shadow: var(--shadow-sm);
}

.navIcon {
  width: var(--spacing-5);
  height: var(--spacing-5);
  margin-right: var(--spacing-3);
  flex-shrink: 0;
  transition: var(--transition-colors);
  color: var(--color-gray-400);
}

.navIconActive {
  color: var(--color-primary-600);
}

.navText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navIndicator {
  position: absolute;
  right: var(--spacing-2);
  width: var(--spacing-2);
  height: var(--spacing-2);
  background-color: var(--color-primary-500);
  border-radius: var(--radius-full);
}

.sidebarFooter {
  flex-shrink: 0;
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.footerContent {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.footerIcon {
  width: var(--spacing-4);
  height: var(--spacing-4);
  margin-right: var(--spacing-2);
  flex-shrink: 0;
}

.mainContent {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.mobileTopBar {
  position: sticky;
  top: 0;
  z-index: var(--z-30);
  display: flex;
  height: var(--spacing-16);
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.mobileMenuButton {
  padding: var(--spacing-4);
  color: var(--color-gray-500);
  transition: var(--transition-colors);
}

.mobileMenuButton:hover {
  color: var(--color-gray-700);
  background-color: var(--color-gray-50);
}

.mobileMenuButton:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px var(--color-primary-500);
}

.mobileTitle {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 0 var(--spacing-4);
}

.mobileTitleText {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pageContent {
  flex: 1;
  background-color: var(--color-gray-50);
}

.pageContentInner {
  padding: var(--spacing-6) 0;
}

.pageContainer {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Responsive styles */
@media (min-width: 640px) {
  .pageContainer {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .backdrop {
    display: none;
  }
  
  .sidebar {
    transform: translateX(0);
    box-shadow: none;
  }
  
  .mainContent {
    padding-left: var(--spacing-64);
  }
  
  .mobileTopBar {
    display: none;
  }
  
  .pageContainer {
    padding: 0 var(--spacing-8);
  }
}
