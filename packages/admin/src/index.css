@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import './styles/base.css';
@import './styles/layout.css';
@import './styles/components.css';
@import './styles/pages.css';

/* Additional custom styles specific to the admin panel */

/* Sidebar enhancements */
.sidebar-nav-item {
  position: relative;
}

.sidebar-nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--color-primary-500);
  transform: scaleY(0);
  transition: transform 0.2s ease-in-out;
  transform-origin: top;
}

.sidebar-nav-item.active::before {
  transform: scaleY(1);
}

/* Smooth scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
  width: 4px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 2px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}
