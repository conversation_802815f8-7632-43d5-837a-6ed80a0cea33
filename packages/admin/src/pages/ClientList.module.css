/* ClientList component styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerContent h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}

.headerContent p {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.newClientButton {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid transparent;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  color: white;
  background-color: var(--color-primary-600);
  text-decoration: none;
  transition: var(--transition-colors);
}

.newClientButton:hover {
  background-color: var(--color-primary-700);
}

.searchContainer {
  background-color: white;
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.searchWrapper {
  position: relative;
  max-width: 24rem;
}

.searchIcon {
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  width: var(--spacing-5);
  height: var(--spacing-5);
  color: var(--color-gray-400);
}

.searchInput {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-10);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  transition: var(--transition-colors);
}

.searchInput:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 1px var(--color-primary-500);
}

.clientsGrid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: var(--spacing-6);
}

.clientCard {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-6);
  transition: var(--transition-all);
}

.clientCard:hover {
  box-shadow: var(--shadow-md);
}

.clientHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.clientInfo {
  display: flex;
  align-items: center;
}

.clientAvatar {
  width: var(--spacing-12);
  height: var(--spacing-12);
  background-color: var(--color-primary-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-4);
}

.clientAvatarText {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary-700);
}

.clientDetails h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.clientDetails p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.clientActions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.actionButton {
  padding: var(--spacing-2);
  color: var(--color-gray-400);
  border-radius: var(--radius-md);
  transition: var(--transition-colors);
}

.actionButton:hover {
  color: var(--color-gray-600);
  background-color: var(--color-gray-100);
}

.clientStats {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: var(--spacing-4);
}

.statItem {
  text-align: center;
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.statLabel {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.clientFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
}

.clientStatus {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
}

.statusDot {
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--radius-full);
  margin-right: var(--spacing-2);
}

.statusActive {
  background-color: var(--color-green-400);
}

.statusInactive {
  background-color: var(--color-gray-400);
}

.viewButton {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary-600);
  border: 1px solid var(--color-primary-600);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: var(--transition-colors);
}

.viewButton:hover {
  background-color: var(--color-primary-50);
}

.emptyState {
  text-align: center;
  padding: var(--spacing-12) 0;
}

.emptyStateIcon {
  margin: 0 auto var(--spacing-4);
  width: var(--spacing-12);
  height: var(--spacing-12);
  color: var(--color-gray-400);
}

.emptyStateTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.emptyStateDescription {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  margin-bottom: var(--spacing-6);
}

.loadingState {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: var(--spacing-6);
}

.loadingCard {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-6);
}

.loadingHeader {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.loadingAvatar {
  width: var(--spacing-12);
  height: var(--spacing-12);
  background-color: var(--color-gray-200);
  border-radius: var(--radius-lg);
  margin-right: var(--spacing-4);
}

.loadingContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.loadingLine {
  height: var(--spacing-4);
  background-color: var(--color-gray-200);
  border-radius: var(--radius-md);
}

.loadingLineShort {
  width: 75%;
}

/* Responsive styles */
@media (min-width: 768px) {
  .clientsGrid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .clientsGrid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .loadingState {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
