/* Dashboard component styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerContent h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}

.headerContent p {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.newClientButton {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid transparent;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  color: white;
  background-color: var(--color-primary-600);
  text-decoration: none;
  transition: var(--transition-colors);
}

.newClientButton:hover {
  background-color: var(--color-primary-700);
}

.newClientButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: var(--spacing-5);
}

.statCard {
  position: relative;
  background-color: white;
  padding-top: var(--spacing-5);
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
  padding-bottom: var(--spacing-12);
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-lg);
  overflow: hidden;
  text-decoration: none;
  transition: var(--transition-all);
}

.statCard:hover {
  box-shadow: var(--shadow-md);
}

.statIconContainer {
  position: absolute;
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
}

.statIconContainerBlue {
  background-color: var(--color-blue-500);
}

.statIconContainerGreen {
  background-color: var(--color-green-500);
}

.statIconContainerPurple {
  background-color: var(--color-purple-500);
}

.statIconContainerOrange {
  background-color: var(--color-orange-500);
}

.statIcon {
  width: var(--spacing-6);
  height: var(--spacing-6);
  color: white;
}

.statContent {
  margin-left: var(--spacing-16);
}

.statName {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-500);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.statValueContainer {
  margin-left: var(--spacing-16);
  display: flex;
  align-items: baseline;
  padding-bottom: var(--spacing-6);
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.quickActions {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: var(--spacing-6);
}

.actionCard {
  background-color: white;
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  text-decoration: none;
  transition: var(--transition-all);
}

.actionCard:hover {
  box-shadow: var(--shadow-md);
}

.actionCardContent {
  display: flex;
  align-items: center;
}

.actionIcon {
  width: var(--spacing-8);
  height: var(--spacing-8);
  margin-right: var(--spacing-4);
}

.actionIconBlue {
  color: var(--color-blue-500);
}

.actionIconGreen {
  color: var(--color-green-500);
}

.actionIconPurple {
  color: var(--color-purple-500);
}

.actionIconOrange {
  color: var(--color-orange-500);
}

.actionText h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.actionText p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.recentActivity {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-6);
}

.recentActivityHeader {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: var(--spacing-4);
}

.recentActivityTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.activityItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
}

.activityItemContent {
  display: flex;
  align-items: center;
}

.activityItemIcon {
  width: var(--spacing-5);
  height: var(--spacing-5);
  margin-right: var(--spacing-3);
  color: var(--color-gray-400);
}

.activityItemText {
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
}

.activityItemTime {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.emptyState {
  text-align: center;
  padding: var(--spacing-12) 0;
}

.emptyStateIcon {
  margin: 0 auto var(--spacing-2);
  width: var(--spacing-12);
  height: var(--spacing-12);
  color: var(--color-gray-400);
}

.emptyStateTitle {
  margin-top: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.emptyStateDescription {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* Responsive styles */
@media (min-width: 640px) {
  .statsGrid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .statCard {
    padding-top: var(--spacing-6);
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
  
  .statValueContainer {
    padding-bottom: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .statsGrid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .quickActions {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
