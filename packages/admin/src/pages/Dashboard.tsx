/**
 * Dashboard page component for the admin panel.
 * This component displays an overview of the AI Answer Bot system including
 * client statistics, recent activity, and quick access to key management functions.
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  Users,
  MessageSquare,
  ExternalLink,
  TrendingUp,
  Plus,
  Activity
} from 'lucide-react';
import { apiService } from '../services/api';
import styles from './Dashboard.module.css';

const Dashboard: React.FC = () => {
  const { data: clients, isLoading: clientsLoading } = useQuery({
    queryKey: ['clients'],
    queryFn: () => apiService.getClients(1, 10)
  });

  const { data: healthStatus } = useQuery({
    queryKey: ['health'],
    queryFn: () => apiService.healthCheck(),
    refetchInterval: 30000 // Check every 30 seconds
  });

  const stats = [
    {
      name: 'Total Clients',
      value: clients?.total || 0,
      icon: Users,
      color: 'bg-blue-500',
      href: '/clients'
    },
    {
      name: 'Active Bots',
      value: clients?.data?.filter(c => c.config.siteName).length || 0,
      icon: MessageSquare,
      color: 'bg-green-500',
      href: '/bot'
    },
    {
      name: 'Total Interactions',
      value: '1,234', // This would come from analytics
      icon: TrendingUp,
      color: 'bg-purple-500',
      href: '/analytics'
    },
    {
      name: 'System Status',
      value: healthStatus?.status === 'ok' ? 'Healthy' : 'Issues',
      icon: Activity,
      color: healthStatus?.status === 'ok' ? 'bg-green-500' : 'bg-red-500',
      href: '/settings'
    }
  ];

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1>Dashboard</h1>
          <p>
            Welcome to the AI Answer Bot admin panel
          </p>
        </div>
        <Link
          to="/clients/new"
          className={styles.newClientButton}
        >
          <Plus className="w-4 h-4 mr-2" />
          New Client
        </Link>
      </div>

      {/* Stats Grid */}
      <div className={styles.statsGrid}>
        {stats.map((stat) => {
          const Icon = stat.icon;
          const iconContainerClass = stat.color === 'bg-blue-500' ? styles.statIconContainerBlue :
                                    stat.color === 'bg-green-500' ? styles.statIconContainerGreen :
                                    stat.color === 'bg-purple-500' ? styles.statIconContainerPurple :
                                    styles.statIconContainerOrange;
          return (
            <Link
              key={stat.name}
              to={stat.href}
              className={styles.statCard}
            >
              <div>
                <div className={`${styles.statIconContainer} ${iconContainerClass}`}>
                  <Icon className={styles.statIcon} />
                </div>
                <p className={styles.statName}>
                  {stat.name}
                </p>
              </div>
              <div className={styles.statValueContainer}>
                <p className={styles.statValue}>
                  {stat.value}
                </p>
              </div>
            </Link>
          );
        })}
      </div>

    </div>
  );
};

export default Dashboard;
