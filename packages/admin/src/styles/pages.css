/* Shared page component styles */

/* Common page layout */
.pageContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.pageHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pageHeaderContent h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}

.pageHeaderContent p {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.backButton {
  padding: var(--spacing-2);
  color: var(--color-gray-400);
  border-radius: var(--radius-lg);
  transition: var(--transition-colors);
}

.backButton:hover {
  color: var(--color-gray-600);
  background-color: var(--color-gray-100);
}

/* Cards */
.card {
  background-color: white;
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
}

.cardTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.cardIcon {
  width: var(--spacing-5);
  height: var(--spacing-5);
  color: var(--color-gray-400);
}

/* Grids */
.grid1 { display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: var(--spacing-6); }
.grid2 { display: grid; grid-template-columns: repeat(2, minmax(0, 1fr)); gap: var(--spacing-6); }
.grid3 { display: grid; grid-template-columns: repeat(3, minmax(0, 1fr)); gap: var(--spacing-6); }
.grid4 { display: grid; grid-template-columns: repeat(4, minmax(0, 1fr)); gap: var(--spacing-6); }

/* Forms */
.formSection {
  margin-bottom: var(--spacing-6);
}

.formSectionTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.formGroup {
  margin-bottom: var(--spacing-4);
}

.formLabel {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-1);
}

.formInput {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  transition: var(--transition-colors);
}

.formInput:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 1px var(--color-primary-500);
}

.formSelect {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background-color: white;
  transition: var(--transition-colors);
}

.formSelect:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 1px var(--color-primary-500);
}

.formError {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--color-red-600);
}

.formActions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
}

/* Stats */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: var(--spacing-6);
}

.statCard {
  background-color: white;
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.statCardContent {
  display: flex;
  align-items: center;
}

.statIcon {
  width: var(--spacing-8);
  height: var(--spacing-8);
  margin-right: var(--spacing-4);
}

.statIconBlue { color: var(--color-blue-500); }
.statIconGreen { color: var(--color-green-500); }
.statIconPurple { color: var(--color-purple-500); }
.statIconOrange { color: var(--color-orange-500); }

.statText p:first-child {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-500);
}

.statText p:last-child {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

/* Empty states */
.emptyState {
  text-align: center;
  padding: var(--spacing-12) 0;
}

.emptyStateIcon {
  margin: 0 auto var(--spacing-4);
  width: var(--spacing-12);
  height: var(--spacing-12);
  color: var(--color-gray-400);
}

.emptyStateTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.emptyStateDescription {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  margin-bottom: var(--spacing-6);
}

/* Loading states */
.loadingContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.loadingCard {
  background-color: white;
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.loadingHeader {
  height: var(--spacing-8);
  background-color: var(--color-gray-200);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  width: 25%;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.loadingLine {
  height: var(--spacing-4);
  background-color: var(--color-gray-200);
  border-radius: var(--radius-md);
}

.loadingLineShort { width: 75%; }
.loadingLineMedium { width: 50%; }

/* Status indicators */
.statusIndicator {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
}

.statusDot {
  width: var(--spacing-3);
  height: var(--spacing-3);
  border-radius: var(--radius-full);
  margin-right: var(--spacing-2);
}

.statusOnline { background-color: var(--color-green-400); }
.statusOffline { background-color: var(--color-red-400); }
.statusActive { background-color: var(--color-green-600); }
.statusInactive { background-color: var(--color-gray-400); }

/* Responsive breakpoints */
@media (min-width: 640px) {
  .statsContainer {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .grid2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .statsContainer {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .grid2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}
