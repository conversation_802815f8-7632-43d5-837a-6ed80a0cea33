/* Base styles and resets */
@import './variables.css';

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: var(--font-family-sans);
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
  background-color: transparent;
  background-image: none;
  border: 0;
  cursor: pointer;
}

/* Remove default input styles */
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

/* Remove default list styles */
ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: inherit;
}

/* Remove default table styles */
table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

/* Remove default fieldset styles */
fieldset {
  margin: 0;
  padding: 0;
  border: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}

/* Remove default textarea resize */
textarea {
  resize: vertical;
}

/* Remove default placeholder opacity */
input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: var(--color-gray-400);
}

/* Remove default button focus styles */
button:focus,
[role="button"]:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Remove default summary styles */
summary {
  display: list-item;
}

/* Ensure images are responsive */
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

/* Ensure hidden elements are hidden */
[hidden] {
  display: none;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Focus styles */
.focus-ring {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Root container */
#root {
  width: 100%;
  margin: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
